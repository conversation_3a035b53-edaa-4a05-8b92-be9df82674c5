#!/usr/bin/env python3
"""
分步执行Agent

负责使用AI决策执行单个步骤，让大模型自己发挥
给大模型所有动作列表，让它自己判断直到finished或失败
"""
import re
import time
from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError
from datetime import datetime
from typing import Dict

from loguru import logger

from src.domain.ui_task.mobile.aggregate.prompt.step_execute_prompt import build_step_execution_prompt
from src.domain.ui_task.mobile.android.action_tool import execute_simple_action
from src.domain.ui_task.mobile.android.image_processor import image_annotator
from src.domain.ui_task.mobile.android.screenshot_manager import convert_screenshot_to_base64, take_screenshot
from src.domain.ui_task.mobile.repo.do.State import DeploymentState
from src.infra.model import get_chat_model


class StepExecutionAgent:
    """分步执行Agent - 负责AI决策的单步骤执行"""

    def __init__(self):
        # 使用配置化的视觉模型
        self.vision = get_chat_model(model_name="vision")

    def _invoke_model_with_timeout_and_retry(self, messages, task_id, timeout=30, max_retries=3):
        """
        带超时和重试机制的模型调用
        前两次尝试有超时限制，第三次（最后一次）不设置超时限制

        Args:
            messages: 消息列表
            task_id: 任务ID
            timeout: 超时时间（秒），默认30秒，仅对前两次尝试有效
            max_retries: 最大重试次数，默认3次

        Returns:
            模型响应对象

        Raises:
            Exception: 所有重试都失败时抛出异常
        """
        for attempt in range(max_retries):
            try:
                # 判断是否为最后一次尝试
                is_last_attempt = (attempt == max_retries - 1)

                if is_last_attempt:
                    logger.info(f"[{task_id}] 🤖 模型调用最后尝试 {attempt + 1}/{max_retries} (无超时限制)")
                else:
                    logger.info(f"[{task_id}] 🤖 模型调用尝试 {attempt + 1}/{max_retries} (超时限制: {timeout}秒)")

                # 最后一次尝试不设置超时限制
                if is_last_attempt:
                    # 直接调用，不设置超时
                    result = self.vision.invoke(messages)
                    logger.info(f"[{task_id}] ✅ 模型调用成功 (最后尝试，无超时限制)")
                    return result
                else:
                    # 使用ThreadPoolExecutor来处理超时
                    with ThreadPoolExecutor(max_workers=1) as executor:
                        # 提交模型调用任务
                        future = executor.submit(self.vision.invoke, messages)

                        try:
                            # 等待结果，设置超时
                            result = future.result(timeout=timeout)
                            logger.info(f"[{task_id}] ✅ 模型调用成功 (尝试 {attempt + 1}/{max_retries})")
                            return result

                        except FutureTimeoutError:
                            # 超时了，取消任务
                            future.cancel()
                            logger.warning(f"[{task_id}] ⏰ 模型调用超时 (尝试 {attempt + 1}/{max_retries})")
                            if attempt < max_retries - 1:
                                logger.info(f"[{task_id}] 🔄 将在2秒后重试...")
                                time.sleep(2)
                                continue
                            else:
                                raise TimeoutError(f"模型调用在{timeout}秒后超时，已重试{max_retries}次")

            except Exception as e:
                logger.warning(f"[{task_id}] ❌ 模型调用失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                if attempt < max_retries - 1:
                    logger.info(f"[{task_id}] 🔄 将在2秒后重试...")
                    time.sleep(2)
                else:
                    logger.error(f"[{task_id}] 💥 所有重试都失败，放弃模型调用")
                    raise e

    def execute_step_with_ai_decision(self, state: DeploymentState, step_description: str,
                                      expected_result: Dict = None, max_step_attempts: int = 7) -> str:
        """
        使用AI决策执行单个步骤，让大模型自己发挥
        给大模型所有动作列表，让它自己判断直到finished或失败

        Args:
            state: 当前状态
            step_description: 步骤描述
            expected_result: 预期结果字典，包含text、image、wait_time
            max_step_attempts: 单个步骤最大尝试次数，默认7次

        Returns:
            "finished": 步骤完成
            "failed": 步骤失败
            "continue": 继续执行
        """
        task_id = state["task_id"]
        screenshot_path = ""

        # 检查任务是否被停止
        if self._check_task_stopped(task_id):
            logger.info(f"[{task_id}] 🛑 Task stopped by user during step execution")
            return "failed"

        # 检查当前步骤的执行次数
        current_step_index = state.get("current_step_index", 0)
        step_attempt_count = self._get_step_attempt_count(state, current_step_index)

        if step_attempt_count > max_step_attempts:
            error_msg = f"步骤执行次数超过最大限制({max_step_attempts}次)，主动退出"
            logger.error(f"[{task_id}] ❌ {error_msg}")

            # 更新任务状态为失败
            self._update_task_status_to_failed(task_id, error_msg)
            return "failed"

        logger.info(f"[{task_id}] 🔄 Step attempt {step_attempt_count + 1}/{max_step_attempts}")

        # 创建动作记录（在等待截图开始前）
        current_action = self._create_step_action_record(task_id, step_description)

        # 记录步骤开始日志
        self._log_step_start(task_id, step_description)

        try:
            # 等待截图（根据预期结果中的等待时间）
            self._wait_before_screenshot(expected_result, task_id)

            screenshot_path = take_screenshot(
                device=state["device"],
                task_id=task_id,
                action_name="step_ai_decision"
            )

            image_data_base64 = convert_screenshot_to_base64(screenshot_path, task_id)

            # 构建给大模型的提示（不包含历史记录，历史记录通过messages数组传递）
            ai_prompt = self._build_step_execution_prompt(step_description, expected_result, state)
            # 构建包含历史对话的消息数组
            messages = self._build_step_execution_messages(ai_prompt, image_data_base64, state)

            # 调用视觉模型进行分步执行（带超时和重试机制）
            model_response_obj = self._invoke_model_with_timeout_and_retry(messages, task_id)
            ai_response = model_response_obj.content
            logger.info(f"[{task_id}] 🤖 AI response: {ai_response}")

            # 更新动作记录的决策内容
            if current_action:
                self._update_action_decision_content(current_action, ai_response)

            # 记录AI决策日志
            self._log_ai_decision(task_id, step_description, ai_response)

            # 解析AI响应，提取动作和状态
            parsed_result = self._parse_ai_response(ai_response)

            if not parsed_result:
                logger.warning(f"[{task_id}] ⚠️ Failed to parse AI response")
                # 记录失败的执行历史
                self._record_step_execution_history(state, step_description, ai_response, "", "failed", screenshot_path)
                self._complete_step_action_record(current_action, False, "AI响应解析失败")
                return "continue"

            action_command = parsed_result.get("action")
            step_status = parsed_result.get("status", "continue")

            logger.info(f"[{task_id}] 🤖 AI Status: {step_status}")

            # 更新动作记录的动作命令
            if current_action and action_command:
                self._update_action_command(current_action, action_command)

            # 记录执行历史
            self._record_step_execution_history(state, step_description, ai_response, action_command, step_status, screenshot_path)

            if step_status == "finished":
                logger.info(f"[{task_id}] ✅ AI determined step is finished")
                self._log_step_completion(task_id, step_description, True, "AI判断步骤完成")
                self._complete_step_action_record(current_action, True, "步骤完成")
                return "finished"
            elif step_status == "failed":
                logger.error(f"[{task_id}] ❌ AI determined step failed")
                self._log_step_completion(task_id, step_description, False, "AI判断步骤失败")
                self._complete_step_action_record(current_action, False, "AI判断步骤失败")
                return "failed"
            elif action_command:
                # 在执行动作前再次检查任务是否被停止
                if self._check_task_stopped(task_id):
                    logger.info(f"[{task_id}] 🛑 Task stopped by user before action execution")
                    self._complete_step_action_record(current_action, False, "用户停止任务")
                    return "failed"

                # 执行AI建议的动作
                execution_result = self._execute_parsed_action(action_command, state, task_id, screenshot_path,
                                                               current_action)
                if execution_result == "success":
                    logger.info(f"[{task_id}] ✅ Action executed successfully")
                    self._log_action_execution(task_id, action_command, True, "动作执行成功")
                    self._complete_step_action_record(current_action, True, "动作执行成功")
                    return "continue"
                elif execution_result == "failed":
                    logger.error(f"[{task_id}] ❌ Action execution failed")
                    self._log_action_execution(task_id, action_command, False, "动作执行失败")
                    self._complete_step_action_record(current_action, False, "动作执行失败")
                    return "failed"
                else:
                    self._log_action_execution(task_id, action_command, True, "动作执行继续")
                    self._complete_step_action_record(current_action, True, "动作执行继续")
                    return "continue"
            else:
                logger.warning(f"[{task_id}] ⚠️ No valid action from AI")
                self._complete_step_action_record(current_action, True, "无有效动作")
                return "continue"

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error in AI decision execution: {str(e)}")
            # 记录异常的执行历史
            self._record_step_execution_history(state, step_description, str(e), "", "error", screenshot_path)
            # 记录异常日志
            self._log_execution_error(task_id, step_description, str(e))
            self._complete_step_action_record(current_action, False, f"执行异常: {str(e)}")
            return "failed"
        finally:
            wait_time = 1.0
            if expected_result and "wait_time" in expected_result:
                wait_time = expected_result["wait_time"]
            time.sleep(wait_time)

            if current_action and screenshot_path:
                self._update_action_screenshot(current_action, screenshot_path)

    def _wait_before_screenshot(self, expected_result: Dict = None, task_id: str = None):
        """
        在截图前等待指定时间
        """
        wait_time = 1.0
        if expected_result and "wait_time" in expected_result:
            wait_time = expected_result["wait_time"]

        logger.info(f"[{task_id}] ⏱️ Waiting {wait_time}s before taking screenshot")
        time.sleep(wait_time)

    @staticmethod
    def _record_step_execution_history(state: DeploymentState, step_description: str,
                                       ai_response: str, action_command: str, step_status: str, screenshot_path: str = ""):
        """
        记录步骤执行历史

        Args:
            state: 当前状态
            step_description: 步骤描述
            ai_response: AI响应内容
            action_command: 执行的动作命令
            step_status: 步骤状态
            screenshot_path: 截图路径
        """
        from datetime import datetime

        current_step_index = state.get("current_step_index", 0)

        history_entry = {
            "action": "step_execution_with_ai",
            "step_index": current_step_index,
            "step_description": step_description,
            "ai_response": ai_response,
            "action_command": action_command,
            "step_status": step_status,
            "screenshot_path": screenshot_path,
            "timestamp": datetime.now().isoformat()
        }

        state["history"].append(history_entry)

    @staticmethod
    def _build_step_execution_prompt(step_description: str, expected_result: Dict = None, state: DeploymentState = None) -> str:
        """
        构建给大模型的步骤执行提示，包含步骤、预期结果和已执行步骤
        """
        expected_text = ""
        if expected_result:
            expected_text = expected_result.get("text", "")

        # 获取已执行的步骤信息
        executed_steps = ""
        if state:
            executed_steps = StepExecutionAgent._get_executed_steps_text(state)

        return build_step_execution_prompt(step_description, expected_text, executed_steps)

    @staticmethod
    def _get_executed_steps_text(state: DeploymentState) -> str:
        """
        获取已执行步骤的文本描述

        Args:
            state: 当前状态

        Returns:
            已执行步骤的文本描述
        """
        task_steps = state.get("task_steps", [])
        current_step_index = state.get("current_step_index", 0)

        if not task_steps or current_step_index <= 0:
            return ""

        executed_steps = []
        for i in range(current_step_index):
            if i < len(task_steps):
                step_description = task_steps[i]
                executed_steps.append(f"{step_description}")

        if executed_steps:
            return "\n".join(executed_steps)
        else:
            return ""

    @staticmethod
    def _get_history_parameters(state: DeploymentState):
        """
        获取历史参数，用于构建消息

        Args:
            state: 当前状态

        Returns:
            execution_records: 执行记录列表
            has_execution_history: 是否有执行历史
        """
        history = state.get("history", [])
        current_step_index = state.get("current_step_index", 0)

        # 获取当前步骤的执行记录
        execution_records = [r for r in history if
                             r.get("action") == "step_execution_with_ai" and
                             r.get("step_index") == current_step_index]

        has_execution_history = len(execution_records) > 0

        return execution_records, has_execution_history

    @staticmethod
    def _build_step_execution_messages(ai_prompt: str, current_image_base64: str, state: DeploymentState) -> list:
        """
        构建包含历史对话的消息数组

        Args:
            ai_prompt: 当前的AI提示
            current_image_base64: 当前截图的base64
            state: 当前状态

        Returns:
            包含历史对话的消息列表
        """
        messages = [{
            "role": "system",
            "content": ai_prompt
        }]

        execution_records, has_execution_history = StepExecutionAgent._get_history_parameters(state)

        if has_execution_history and execution_records:
            # 添加历史执行记录，带轮次标记
            for i, record in enumerate(execution_records):
                round_number = i + 1
                messages.append({
                    "role": "assistant",
                    "content": f"第{round_number}轮执行历史: " + record.get("ai_response", "")
                })
        # 添加当前截图
        messages.append({
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "########## 手机截图 ##########"
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{current_image_base64}",
                        "detail": "high"
                    },
                    "detail": "high",
                }
            ]
        })

        messages.append({
            "role": "user",
            "content": """
##################################################################
开始执行，请严格按照<执行流程>执行任务，严格按照<输出格式>输出结果
##################################################################
            """
        })

        return messages

    @staticmethod
    def _parse_ai_response(ai_response: str) -> Dict:
        """
        解析AI响应，提取思考、动作命令和状态

        Returns:
            {
                "thought": "思考内容",
                "action": "动作命令",
                "status": "finished/failed/continue"
            }
        """
        try:
            result = {
                "thought": "",
                "action": "",
                "status": "continue"
            }

            # 使用正则表达式提取Thought和Action
            thought_match = re.search(r'Thought:\s*(.*?)(?=Action:|$)', ai_response, re.DOTALL | re.IGNORECASE)
            if thought_match:
                result["thought"] = thought_match.group(1).strip()

            action_match = re.search(r'Action:\s*(.*?)(?=\n|$)', ai_response, re.DOTALL | re.IGNORECASE)
            if action_match:
                action_part = action_match.group(1).strip()
                result["action"] = action_part
                # 检查是否是finished或failed状态
                if action_part.lower().startswith("finished"):
                    result["status"] = "finished"
                elif action_part.lower().startswith("failed"):
                    result["status"] = "failed"

            # 如果没有找到标准格式，尝试其他解析方式
            if not result["action"]:
                lines = ai_response.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if line.startswith("动作:") or line.startswith("Action:"):
                        action_part = line.split(":", 1)[1].strip()
                        result["action"] = action_part
                        if action_part.lower().startswith("finished"):
                            result["status"] = "finished"
                        elif action_part.lower().startswith("failed"):
                            result["status"] = "failed"
                        break

            return result

        except Exception as e:
            logger.warning(f"Failed to parse AI response: {str(e)}")
            return {
                "thought": "",
                "action": "",
                "status": "continue"
            }

    def _execute_parsed_action(self, action_command: str, state: DeploymentState, task_id: str,
                               current_screenshot: str = None, current_action=None) -> str:
        """
        执行解析后的动作命令，包含图片标记功能

        Args:
            action_command: 动作命令
            state: 当前状态
            task_id: 任务ID
            current_screenshot: 当前截图路径（用于标记）
            current_action: 当前动作记录对象（用于更新数据库）

        Returns:
            "success": 执行成功
            "failed": 执行失败
            "continue": 继续执行
        """
        try:
            logger.info(f"[{task_id}] 🎯 Executing parsed action: {action_command}")

            # 检查是否是finished或failed状态
            if action_command.lower().startswith("finished"):
                # 对于finished动作，需要先更新结束时间（因为不执行ADB命令）
                if current_action:
                    self._update_action_end_time(current_action)
                logger.info(f"[{task_id}] ✅ Step marked as finished by AI")
                return "success"
            elif action_command.lower().startswith("failed"):
                # 对于failed动作，需要先更新结束时间（因为不执行ADB命令）
                if current_action:
                    self._update_action_end_time(current_action)
                logger.error(f"[{task_id}] ❌ Step marked as failed by AI")
                return "failed"
            elif action_command.lower().startswith("print"):
                # 对于print动作，需要先更新结束时间（因为不执行ADB命令）
                if current_action:
                    self._update_action_end_time(current_action)
                # 提取并输出打印内容
                content = self._extract_print_content(action_command)
                logger.info(f"[{task_id}] 📝 Print action: {content}")
                return "success"

            # 检查动作是否需要坐标，如果需要则在执行后进行图片标记
            needs_coordinates = self._requires_coordinates(action_command)

            # 直接执行动作命令（坐标已经在上层获取）
            logger.info(f"[{task_id}] ⚡ Executing action command directly")
            action_result = execute_simple_action(action_command, state["device"])

            # 更新当前action记录的结束时间（命令执行完成后）
            if current_action:
                self._update_action_end_time(current_action)

            # 如果动作执行成功且需要坐标，则在截图上标记操作位置
            if action_result.get("status") == "success" and needs_coordinates and current_screenshot:
                self._annotate_screenshot_after_step_execution(
                    state, action_command, current_screenshot
                )

                # 图片标记完成后，更新数据库中的截图路径（确保数据库记录的是已标记的图片）
                if current_action:
                    self._update_action_screenshot(current_action, current_screenshot)
                    logger.info(f"[{task_id}] 📸 Updated database with annotated screenshot: {current_screenshot}")

            # 检查执行结果
            if action_result.get("status") == "success":
                logger.info(f"[{task_id}] ✅ Action executed successfully")
                return "success"
            else:
                logger.warning(
                    f"[{task_id}] ⚠️ Action execution failed: {action_result.get('message', 'Unknown error')}")
                return "continue"

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error executing parsed action: {str(e)}")
            return "failed"

    @staticmethod
    def _requires_coordinates(action_command: str) -> bool:
        """
        检查动作是否需要坐标

        Args:
            action_command: 动作命令

        Returns:
            是否需要坐标
        """
        action_command_lower = action_command.lower()
        coordinate_actions = ['click', 'long_click', 'input', 'scroll']
        return any(action in action_command_lower for action in coordinate_actions)

    @staticmethod
    def _annotate_screenshot_after_step_execution(state: DeploymentState, action_command: str,
                                                  current_screenshot: str) -> None:
        """
        在分步执行动作完成后对截图进行标注

        Args:
            state: 当前状态
            action_command: 动作命令
            current_screenshot: 当前截图路径
        """
        task_id = state["task_id"]

        try:
            # 执行异步标注（不阻塞主流程）
            image_annotator.annotate_screenshot_from_action_async(
                current_screenshot, action_command, task_id, device=state["device"]
            )
            logger.info(f"[{task_id}] 🎯 Screenshot annotation queued after step execution: {action_command}")

        except Exception as e:
            logger.warning(f"[{task_id}] ⚠️ Failed to queue screenshot annotation after step execution: {str(e)}")

    def _check_task_stopped(self, task_id: str) -> bool:
        """
        检查任务是否被停止

        Args:
            task_id: 任务ID

        Returns:
            任务是否被停止
        """
        from src.domain.ui_task.mobile.repo.do.task_stop_manager import task_stop_manager
        return task_stop_manager.is_task_stopped(task_id)

    @staticmethod
    def _get_step_attempt_count(state: DeploymentState, step_index: int) -> int:
        """
        获取指定步骤的尝试次数

        Args:
            state: 当前状态
            step_index: 步骤索引

        Returns:
            该步骤的尝试次数
        """
        history = state.get("history", [])

        # 统计当前步骤的执行记录数量
        step_attempts = [r for r in history if
                         r.get("action") == "step_execution_with_ai" and
                         r.get("step_index") == step_index]

        return len(step_attempts)

    def _update_task_status_to_failed(self, task_id: str, error_message: str):
        """
        更新任务状态为失败

        Args:
            task_id: 任务ID
            error_message: 失败原因
        """
        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service

            # 更新任务状态为失败
            task_persistence_service.update_task_status(
                task_id=task_id,
                status="failed",
                error_message=error_message
            )

            # 记录失败日志
            self._log_execution_error(task_id, "任务执行", error_message)

            logger.info(f"[{task_id}] 📝 Task status updated to failed: {error_message}")

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Failed to update task status: {str(e)}")

    @staticmethod
    def _create_step_action_record(task_id: str, step_description: str):
        """
        创建步骤动作记录

        Args:
            task_id: 任务ID
            step_description: 步骤描述

        Returns:
            创建的动作记录对象
        """
        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service

            current_action = task_persistence_service.create_task_action(
                task_id=task_id,
                step_name=step_description,
                action_command="",  # 稍后更新
                decision_content="",
                start_time=datetime.now()
            )

            return current_action

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Failed to create step action record: {str(e)}")
            return None

    @staticmethod
    def _update_action_screenshot(current_action, screenshot_path: str):
        """
        更新动作记录的截图路径

        Args:
            current_action: 动作记录对象
            screenshot_path: 截图路径
        """
        if not current_action:
            return

        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service

            task_persistence_service.action_repo.update_action_extra_info(
                action_id=current_action.id,
                before_screenshot=screenshot_path
            )

        except Exception as e:
            logger.error(f"Failed to update action screenshot: {str(e)}")

    @staticmethod
    def _update_action_end_time(current_action):
        """
        更新动作记录的结束时间

        Args:
            current_action: 动作记录对象
        """
        if not current_action:
            return

        try:
            from src.domain.ui_task.mobile.repo.ui_task_repository import DBSessionContext
            from src.domain.ui_task.mobile.repo.dao import UITaskAction
            from datetime import datetime

            with DBSessionContext() as session:
                action = session.query(UITaskAction).filter(UITaskAction.id == current_action.id).first()
                if action:
                    # 只更新结束时间，不改变状态（状态由complete_task_action统一管理）
                    action.end_time = datetime.now()
                    action.updated_at = datetime.now()
                    session.commit()
                    logger.info(f"Updated action end_time for step execution action {action.id}")

        except Exception as e:
            logger.warning(f"Failed to update action end_time: {str(e)}")

    @staticmethod
    def _extract_print_content(action_command: str) -> str:
        """
        从print动作命令中提取内容

        Args:
            action_command: print动作命令

        Returns:
            提取的内容字符串
        """
        import re

        # 尝试提取content参数
        content_match = re.search(r"content='([^']*)'", action_command)
        if not content_match:
            content_match = re.search(r'content="([^"]*)"', action_command)
        if not content_match:
            content_match = re.search(r'content=([^)]+)', action_command)

        if content_match:
            return content_match.group(1).strip()

        # 如果没有找到content参数，返回空字符串
        return ""

    @staticmethod
    def _update_action_decision_content(current_action, ai_response: str):
        """
        更新动作记录的决策内容

        Args:
            current_action: 动作记录对象
            ai_response: AI响应内容
        """
        if not current_action:
            return

        try:
            from src.domain.ui_task.mobile.repo.ui_task_repository import DBSessionContext
            from src.domain.ui_task.mobile.repo.dao import UITaskAction
            from datetime import datetime

            with DBSessionContext() as session:
                action = session.query(UITaskAction).filter(UITaskAction.id == current_action.id).first()
                if action:
                    action.decision_content = ai_response
                    action.updated_at = datetime.now()
                    session.commit()

        except Exception as e:
            logger.error(f"Failed to update action decision content: {str(e)}")

    @staticmethod
    def _update_action_command(current_action, action_command: str):
        """
        更新动作记录的动作命令

        Args:
            current_action: 动作记录对象
            action_command: 动作命令
        """
        if not current_action:
            return

        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service

            task_persistence_service.action_repo.update_action_extra_info(
                action_id=current_action.id,
                final_action_command=action_command
            )

        except Exception as e:
            logger.error(f"Failed to update action command: {str(e)}")

    @staticmethod
    def _complete_step_action_record(current_action, success: bool, message: str = None):
        """
        完成步骤动作记录

        Args:
            current_action: 动作记录对象
            success: 是否成功
            message: 完成消息
        """
        if not current_action:
            return

        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service

            task_persistence_service.complete_task_action(
                action_id=current_action.id,
                success=success,
                error_message=message if not success else None
            )

            status = "成功" if success else "失败"
            logger.info(f"Step action record completed: {status} - {message or ''}")

        except Exception as e:
            logger.error(f"Failed to complete step action record: {str(e)}")

    @staticmethod
    def _log_step_start(task_id: str, step_description: str):
        """
        异步记录步骤开始日志

        Args:
            task_id: 任务ID
            step_description: 步骤描述
        """
        try:
            from src.domain.ui_task.mobile.service.async_log_service import async_log_service

            async_log_service.log_step_async(task_id, 'start', step_description)

        except Exception as e:
            logger.warning(f"[{task_id}] Failed to queue step start log: {str(e)}")

    @staticmethod
    def _log_ai_decision(task_id: str, step_description: str, ai_response: str):
        """
        异步记录AI决策日志

        Args:
            task_id: 任务ID
            step_description: 步骤描述
            ai_response: AI响应内容
        """
        try:
            from src.domain.ui_task.mobile.service.async_log_service import async_log_service

            async_log_service.log_step_async(task_id, 'ai_decision', ai_response, step_description)

        except Exception as e:
            logger.warning(f"[{task_id}] Failed to queue AI decision log: {str(e)}")

    @staticmethod
    def _log_step_completion(task_id: str, step_description: str, success: bool, message: str):
        """
        异步记录步骤完成日志

        Args:
            task_id: 任务ID
            step_description: 步骤描述
            success: 是否成功
            message: 完成消息
        """
        try:
            from src.domain.ui_task.mobile.service.async_log_service import async_log_service

            status = "成功" if success else "失败"
            completion_message = f"{step_description} - {status}: {message}"
            async_log_service.log_step_async(task_id, 'completion', completion_message)

        except Exception as e:
            logger.warning(f"[{task_id}] Failed to queue step completion log: {str(e)}")

    @staticmethod
    def _log_action_execution(task_id: str, action_command: str, success: bool, message: str):
        """
        异步记录动作执行日志

        Args:
            task_id: 任务ID
            action_command: 动作命令
            success: 是否成功
            message: 执行消息
        """
        try:
            from src.domain.ui_task.mobile.service.async_log_service import async_log_service

            status = "成功" if success else "失败"
            action_message = f"{action_command} - {status}: {message}"
            async_log_service.log_step_async(task_id, 'action', action_message)

        except Exception as e:
            logger.warning(f"[{task_id}] Failed to queue action execution log: {str(e)}")

    @staticmethod
    def _log_execution_error(task_id: str, step_description: str, error_message: str):
        """
        异步记录执行错误日志

        Args:
            task_id: 任务ID
            step_description: 步骤描述
            error_message: 错误消息
        """
        try:
            from src.domain.ui_task.mobile.service.async_log_service import async_log_service

            error_msg = f"步骤执行异常: {step_description} - {error_message}"
            async_log_service.log_error_async(task_id, error_msg)

        except Exception as e:
            logger.warning(f"[{task_id}] Failed to queue execution error log: {str(e)}")
